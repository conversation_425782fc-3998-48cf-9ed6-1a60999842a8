/**
 * Quick connection test utility
 * Helps diagnose Supabase connection issues
 */

import { supabase } from '../supabase/client';

export class ConnectionTest {
  static async runQuickTest() {
    const results = {
      timestamp: new Date().toISOString(),
      tests: {}
    };

    console.log('🔍 Running quick connection test...');

    // Test 1: Basic URL accessibility
    try {
      const response = await fetch(supabase.supabaseUrl + '/rest/v1/', {
        method: 'HEAD',
        headers: {
          'apikey': supabase.supabaseKey
        }
      });
      
      results.tests.urlAccessible = {
        success: response.ok,
        status: response.status,
        statusText: response.statusText
      };
      
      console.log('✅ URL accessibility:', response.ok ? 'Success' : 'Failed');
    } catch (error) {
      results.tests.urlAccessible = {
        success: false,
        error: error.message
      };
      console.log('❌ URL accessibility failed:', error.message);
    }

    // Test 2: Auth endpoint
    try {
      const authTest = await Promise.race([
        supabase.auth.getSession(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Auth timeout')), 3000)
        )
      ]);
      
      results.tests.authEndpoint = {
        success: true,
        hasSession: !!authTest.data?.session
      };
      
      console.log('✅ Auth endpoint: Success');
    } catch (error) {
      results.tests.authEndpoint = {
        success: false,
        error: error.message
      };
      console.log('❌ Auth endpoint failed:', error.message);
    }

    // Test 3: Database connectivity
    try {
      const dbTest = await Promise.race([
        supabase.from('user_profiles').select('count').limit(1),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('DB timeout')), 3000)
        )
      ]);
      
      results.tests.database = {
        success: !dbTest.error,
        error: dbTest.error?.message
      };
      
      console.log('✅ Database connectivity:', !dbTest.error ? 'Success' : 'Failed');
    } catch (error) {
      results.tests.database = {
        success: false,
        error: error.message
      };
      console.log('❌ Database connectivity failed:', error.message);
    }

    // Test 4: Network conditions
    const networkStart = performance.now();
    try {
      await fetch('https://www.google.com/favicon.ico', { 
        method: 'HEAD',
        mode: 'no-cors'
      });
      const networkTime = performance.now() - networkStart;
      
      results.tests.network = {
        success: true,
        latency: Math.round(networkTime)
      };
      
      console.log('✅ Network conditions: Good');
    } catch (error) {
      results.tests.network = {
        success: false,
        error: error.message
      };
      console.log('❌ Network conditions: Poor');
    }

    // Summary
    const successCount = Object.values(results.tests).filter(test => test.success).length;
    const totalTests = Object.keys(results.tests).length;
    
    results.summary = {
      passed: successCount,
      total: totalTests,
      success: successCount === totalTests
    };

    console.log(`🏁 Connection test completed: ${successCount}/${totalTests} tests passed`);
    
    return results;
  }

  static async diagnoseSlowLoading() {
    console.log('🔍 Diagnosing slow loading issues...');
    
    const diagnosis = {
      timestamp: new Date().toISOString(),
      issues: [],
      recommendations: []
    };

    // Check localStorage size
    try {
      const localStorageSize = JSON.stringify(localStorage).length;
      if (localStorageSize > 1000000) { // 1MB
        diagnosis.issues.push('Large localStorage detected');
        diagnosis.recommendations.push('Clear browser storage');
      }
    } catch (error) {
      diagnosis.issues.push('Cannot access localStorage');
    }

    // Check for stuck auth states
    try {
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('supabase') || key.includes('sb-')
      );
      
      if (authKeys.length > 5) {
        diagnosis.issues.push('Multiple Supabase auth keys found');
        diagnosis.recommendations.push('Clear Supabase auth data');
      }
    } catch (error) {
      diagnosis.issues.push('Cannot check auth keys');
    }

    // Check session storage
    try {
      const sessionSize = JSON.stringify(sessionStorage).length;
      if (sessionSize > 100000) { // 100KB
        diagnosis.issues.push('Large sessionStorage detected');
        diagnosis.recommendations.push('Clear session storage');
      }
    } catch (error) {
      diagnosis.issues.push('Cannot access sessionStorage');
    }

    // Run connection test
    const connectionResults = await this.runQuickTest();
    if (!connectionResults.summary.success) {
      diagnosis.issues.push('Connection issues detected');
      diagnosis.recommendations.push('Check network connection');
    }

    console.log('🏁 Diagnosis completed:', diagnosis);
    return diagnosis;
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.ConnectionTest = ConnectionTest;
  console.log('🔧 ConnectionTest available globally as window.ConnectionTest');
  console.log('🔧 Use ConnectionTest.runQuickTest() to test connection');
  console.log('🔧 Use ConnectionTest.diagnoseSlowLoading() to diagnose issues');
}

export default ConnectionTest;
