# API Usage Tracking System

This guide explains how to set up and use the API usage tracking system for monitoring client API usage, costs, and performance.

## Overview

The API usage tracking system provides comprehensive monitoring of:
- API request counts and success rates
- Token usage and cost estimation
- Model and endpoint usage patterns
- User-specific usage statistics
- Real-time monitoring and historical analytics

## Architecture

### Components

1. **Database Layer** (Supabase)
   - `api_usage_logs` table for storing usage data
   - SQL functions for aggregation and analytics
   - Row-level security for data protection

2. **Backend Layer** (Railway)
   - Usage logging middleware in FastAPI
   - Admin endpoints for retrieving statistics
   - Cost calculation and token estimation

3. **Frontend Layer** (React)
   - Admin dashboard for usage monitoring
   - Charts and visualizations
   - Export and filtering capabilities

## Setup Instructions

### 1. Database Setup

Run the SQL script to create the necessary database tables and functions:

```bash
# Execute the SQL file in your Supabase SQL editor
frontend/src/supabase/create-api-usage-table.sql
```

This creates:
- `api_usage_logs` table with proper indexes
- Cost calculation function
- Usage statistics aggregation function
- Row-level security policies

### 2. Backend Configuration

Add the required environment variables to your Railway deployment:

```bash
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

The Railway backend will automatically:
- Log all API requests to the database
- Calculate token usage and costs
- Provide admin endpoints for statistics

### 3. Frontend Integration

The API Usage admin page is automatically available in the Admin Settings:
- Navigate to Admin Settings → API Usage
- View real-time usage statistics
- Export usage data as CSV
- Filter by date, user, model, or endpoint

## Features

### Usage Monitoring

**Overview Dashboard:**
- Today's usage statistics
- Monthly usage summary
- Last 30 days trends
- Top models and endpoints

**Detailed Logs:**
- Complete request history
- Success/failure tracking
- Response times and token counts
- Cost estimates per request

**Analytics:**
- Usage trends over time
- Model performance comparison
- User activity patterns
- Cost optimization insights

### Cost Tracking

The system automatically calculates costs based on current pricing:

**OpenAI Models:**
- GPT-4o: $0.0025/1K prompt tokens, $0.01/1K completion tokens
- GPT-4: $0.03/1K prompt tokens, $0.06/1K completion tokens
- GPT-3.5-turbo: $0.0015/1K prompt tokens, $0.002/1K completion tokens
- o1-preview: $0.015/1K prompt tokens, $0.06/1K completion tokens
- o1-mini: $0.003/1K prompt tokens, $0.012/1K completion tokens

**Anthropic Models:**
- Claude-3-opus: $0.015/1K prompt tokens, $0.075/1K completion tokens
- Claude-3-sonnet: $0.003/1K prompt tokens, $0.015/1K completion tokens
- Claude-3-haiku: $0.00025/1K prompt tokens, $0.00125/1K completion tokens

**Google Gemini:**
- Gemini-1.5-pro: $0.00125/1K prompt tokens, $0.005/1K completion tokens
- Gemini-1.5-flash: $0.000075/1K prompt tokens, $0.0003/1K completion tokens

**Other Models:**
- DeepSeek: $0.00014/1K prompt tokens, $0.00028/1K completion tokens
- Groq (Llama): $0.0001/1K tokens (both prompt and completion)

### Data Export

Export usage data in CSV format with the following columns:
- Date and time
- User email
- Endpoint and model used
- Token counts (prompt, completion, total)
- Request duration
- Success status
- Cost estimate
- Thinking mode flag

## API Endpoints

### Admin Endpoints (Railway Backend)

**Get Usage Statistics:**
```
GET /api/admin/usage/stats
Query Parameters:
- start_date: Start date (YYYY-MM-DD)
- end_date: End date (YYYY-MM-DD)
- user_id: Filter by user ID
```

**Get Usage Logs:**
```
GET /api/admin/usage/logs
Query Parameters:
- limit: Number of records (default: 100)
- offset: Pagination offset (default: 0)
- user_id: Filter by user ID
- endpoint: Filter by endpoint
- model: Filter by model
- start_date: Start date filter
- end_date: End date filter
```

**Get Usage Summary:**
```
GET /api/admin/usage/summary
Returns: Today, this month, and last 30 days statistics
```

### Database Functions

**get_api_usage_stats:**
```sql
SELECT * FROM get_api_usage_stats(
  p_start_date => '2024-01-01',
  p_end_date => '2024-01-31',
  p_user_id => 'optional-user-id'
);
```

**calculate_api_cost:**
```sql
SELECT calculate_api_cost('gpt-4o', 1000, 500);
-- Returns estimated cost for 1000 prompt tokens and 500 completion tokens
```

## Usage Examples

### Frontend Service Usage

```javascript
import { ApiUsageService } from '../services/apiUsageService';

// Get usage statistics
const stats = await ApiUsageService.getUsageStats({
  startDate: '2024-01-01',
  endDate: '2024-01-31'
});

// Get usage logs with pagination
const logs = await ApiUsageService.getUsageLogs({
  limit: 50,
  offset: 0,
  endpoint: 'openai'
});

// Export usage data
const csvData = await ApiUsageService.exportUsageData({
  startDate: '2024-01-01',
  endDate: '2024-01-31'
});
```

### Direct Database Queries

```sql
-- Get today's usage by model
SELECT 
  model,
  COUNT(*) as requests,
  SUM(total_tokens) as total_tokens,
  SUM(cost_estimate) as total_cost
FROM api_usage_logs 
WHERE request_date = CURRENT_DATE
GROUP BY model
ORDER BY requests DESC;

-- Get user usage summary
SELECT 
  user_email,
  COUNT(*) as total_requests,
  SUM(total_tokens) as total_tokens,
  SUM(cost_estimate) as total_cost
FROM api_usage_logs 
WHERE request_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY user_email
ORDER BY total_cost DESC;
```

## Security Considerations

### Row-Level Security (RLS)

The system implements RLS policies to ensure:
- Users can only view their own usage data
- Admins can view all usage data
- System can insert usage logs without user restrictions

### Data Privacy

- User emails are stored for admin monitoring
- No sensitive prompt content is logged
- Only metadata and usage statistics are tracked

### Access Control

- Admin endpoints require proper authentication
- Frontend components check admin permissions
- Database functions respect user roles

## Monitoring and Alerts

### Performance Monitoring

Track these key metrics:
- Request success rates
- Average response times
- Token usage trends
- Cost per user/model

### Cost Alerts

Set up alerts for:
- Daily cost thresholds
- Unusual usage patterns
- High-cost model usage
- User quota exceeded

## Troubleshooting

### Common Issues

**Usage not being logged:**
1. Check Supabase credentials in Railway environment
2. Verify database table exists and has correct permissions
3. Check Railway logs for error messages

**Incorrect cost calculations:**
1. Verify model pricing in the cost calculation function
2. Update pricing if models have changed
3. Check token estimation accuracy

**Admin page not loading:**
1. Verify user has admin role in user_profiles table
2. Check browser console for JavaScript errors
3. Ensure Supabase connection is working

### Debugging

Enable debug logging in the Railway backend:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

Check Supabase logs for database errors:
- Go to Supabase Dashboard → Logs
- Filter by timestamp and error level

## Future Enhancements

### Planned Features

1. **Real-time Dashboard**
   - Live usage updates
   - Real-time cost tracking
   - Active user monitoring

2. **Advanced Analytics**
   - Usage prediction models
   - Cost optimization recommendations
   - Performance benchmarking

3. **Alerting System**
   - Email notifications for thresholds
   - Slack integration
   - Custom alert rules

4. **Billing Integration**
   - Automated billing based on usage
   - Invoice generation
   - Payment processing

### Integration Opportunities

1. **External Analytics**
   - Google Analytics integration
   - Mixpanel event tracking
   - Custom dashboard tools

2. **Monitoring Services**
   - Datadog integration
   - New Relic monitoring
   - Prometheus metrics

3. **Business Intelligence**
   - Power BI dashboards
   - Tableau integration
   - Custom reporting tools

## Support

For issues or questions about the API usage tracking system:

1. Check the troubleshooting section above
2. Review Railway and Supabase logs
3. Verify database schema and permissions
4. Test with sample data to isolate issues

The system is designed to be robust and self-healing, with fallback mechanisms for when external services are unavailable.
