import React from 'react';

/**
 * QuestionnaireButtonGrid Component
 * 
 * A reusable component that renders a grid of questionnaire buttons with consistent styling
 * and behavior across different questionnaire pages.
 * 
 * @param {Object} props - Component props
 * @param {Array} props.questionnaires - Array of questionnaire objects
 * @param {string} props.questionnaires[].key - Unique identifier for the questionnaire
 * @param {string} props.questionnaires[].title - Display title for the questionnaire
 * @param {string} props.questionnaires[].description - Description text for the questionnaire
 * @param {string} props.activeQuestionnaire - Currently active questionnaire key
 * @param {Function} props.onQuestionnaireToggle - Callback function when a questionnaire is clicked
 * @param {string} props.themeColor - Theme color for styling (e.g., 'red', 'amber', 'blue')
 * @param {number} props.columns - Number of columns in the grid (default: 2)
 */
function QuestionnaireButtonGrid({
  questionnaires = [],
  activeQuestionnaire = null,
  onQuestionnaireToggle = () => {},
  themeColor = 'red',
  columns = 2
}) {
  // Define color schemes for different themes
  const colorSchemes = {
    red: {
      bg: 'bg-red-50',
      hoverBg: 'hover:bg-red-100',
      activeBg: 'bg-red-100',
      ring: 'ring-red-400',
      text: 'text-red-800'
    },
    amber: {
      bg: 'bg-amber-50',
      hoverBg: 'hover:bg-amber-100',
      activeBg: 'bg-amber-100',
      ring: 'ring-amber-400',
      text: 'text-amber-800'
    },
    blue: {
      bg: 'bg-blue-50',
      hoverBg: 'hover:bg-blue-100',
      activeBg: 'bg-blue-100',
      ring: 'ring-blue-400',
      text: 'text-blue-800'
    },
    green: {
      bg: 'bg-green-50',
      hoverBg: 'hover:bg-green-100',
      activeBg: 'bg-green-100',
      ring: 'ring-green-400',
      text: 'text-green-800'
    },
    purple: {
      bg: 'bg-purple-50',
      hoverBg: 'hover:bg-purple-100',
      activeBg: 'bg-purple-100',
      ring: 'ring-purple-400',
      text: 'text-purple-800'
    }
  };

  const colors = colorSchemes[themeColor] || colorSchemes.red;

  // Generate grid column classes
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  const gridClass = gridCols[columns] || gridCols[2];

  return (
    <div className={`grid ${gridClass} gap-6 mt-6`}>
      {questionnaires.map((questionnaire) => {
        const isActive = activeQuestionnaire === questionnaire.key;
        
        return (
          <div
            key={questionnaire.key}
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${
              isActive 
                ? `ring-2 ${colors.ring} ${colors.activeBg}` 
                : `${colors.bg} ${colors.hoverBg}`
            }`}
            onClick={() => onQuestionnaireToggle(questionnaire.key)}
          >
            <h3 className={`raleway-title-h3 mb-2 ${colors.text}`}>
              {questionnaire.title}
            </h3>
            <p className="body-text">
              {questionnaire.description}
            </p>
          </div>
        );
      })}
    </div>
  );
}

export default QuestionnaireButtonGrid;
