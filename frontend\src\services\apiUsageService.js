import { supabase } from '../supabase/supabaseClient';
import config from '../config';

/**
 * Service for managing API usage tracking and statistics
 */
export class ApiUsageService {
  
  /**
   * Get API usage statistics
   * @param {Object} options - Query options
   * @param {string} options.startDate - Start date (YYYY-MM-DD)
   * @param {string} options.endDate - End date (YYYY-MM-DD)
   * @param {string} options.userId - Filter by user ID
   * @returns {Object} Usage statistics
   */
  static async getUsageStats(options = {}) {
    try {
      const { startDate, endDate, userId } = options;
      
      // Try Railway backend first if available
      if (config.RAILWAY_API_BASE_URL) {
        const params = new URLSearchParams();
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (userId) params.append('user_id', userId);
        
        const response = await fetch(
          `${config.RAILWAY_API_BASE_URL}/api/admin/usage/stats?${params}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            }
          }
        );
        
        if (response.ok) {
          const result = await response.json();
          return result.data;
        }
      }
      
      // Fallback to direct Supabase query
      const params = {};
      if (startDate) params.p_start_date = startDate;
      if (endDate) params.p_end_date = endDate;
      if (userId) params.p_user_id = userId;
      
      const { data, error } = await supabase.rpc('get_api_usage_stats', params);
      
      if (error) throw error;
      
      return data && data.length > 0 ? data[0] : {
        total_requests: 0,
        successful_requests: 0,
        failed_requests: 0,
        total_tokens: 0,
        total_cost: 0.0,
        unique_users: 0,
        top_models: [],
        top_endpoints: [],
        daily_usage: []
      };
      
    } catch (error) {
      console.error('Failed to get usage stats:', error);
      throw error;
    }
  }
  
  /**
   * Get API usage logs
   * @param {Object} options - Query options
   * @param {number} options.limit - Number of records to return
   * @param {number} options.offset - Offset for pagination
   * @param {string} options.userId - Filter by user ID
   * @param {string} options.endpoint - Filter by endpoint
   * @param {string} options.model - Filter by model
   * @param {string} options.startDate - Start date filter
   * @param {string} options.endDate - End date filter
   * @returns {Object} Usage logs and count
   */
  static async getUsageLogs(options = {}) {
    try {
      const { 
        limit = 100, 
        offset = 0, 
        userId, 
        endpoint, 
        model, 
        startDate, 
        endDate 
      } = options;
      
      // Try Railway backend first if available
      if (config.RAILWAY_API_BASE_URL) {
        const params = new URLSearchParams();
        params.append('limit', limit.toString());
        params.append('offset', offset.toString());
        if (userId) params.append('user_id', userId);
        if (endpoint) params.append('endpoint', endpoint);
        if (model) params.append('model', model);
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        
        const response = await fetch(
          `${config.RAILWAY_API_BASE_URL}/api/admin/usage/logs?${params}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            }
          }
        );
        
        if (response.ok) {
          const result = await response.json();
          return {
            data: result.data,
            count: result.count
          };
        }
      }
      
      // Fallback to direct Supabase query
      let query = supabase
        .from('api_usage_logs')
        .select('*');
      
      // Apply filters
      if (userId) query = query.eq('user_id', userId);
      if (endpoint) query = query.eq('endpoint', endpoint);
      if (model) query = query.eq('model', model);
      if (startDate) query = query.gte('request_date', startDate);
      if (endDate) query = query.lte('request_date', endDate);
      
      // Apply pagination and ordering
      query = query
        .order('request_timestamp', { ascending: false })
        .range(offset, offset + limit - 1);
      
      const { data, error } = await query;
      
      if (error) throw error;
      
      return {
        data: data || [],
        count: data ? data.length : 0
      };
      
    } catch (error) {
      console.error('Failed to get usage logs:', error);
      throw error;
    }
  }
  
  /**
   * Get usage summary for dashboard
   * @returns {Object} Usage summary with today, this month, and last 30 days
   */
  static async getUsageSummary() {
    try {
      // Try Railway backend first if available
      if (config.RAILWAY_API_BASE_URL) {
        const response = await fetch(
          `${config.RAILWAY_API_BASE_URL}/api/admin/usage/summary`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            }
          }
        );
        
        if (response.ok) {
          const result = await response.json();
          return result.data;
        }
      }
      
      // Fallback to calculating summary manually
      const today = new Date().toISOString().split('T')[0];
      const monthStart = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        .toISOString().split('T')[0];
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        .toISOString().split('T')[0];
      
      const [todayStats, monthStats, thirtyDayStats] = await Promise.all([
        this.getUsageStats({ startDate: today, endDate: today }),
        this.getUsageStats({ startDate: monthStart, endDate: today }),
        this.getUsageStats({ startDate: thirtyDaysAgo, endDate: today })
      ]);
      
      return {
        today: todayStats,
        this_month: monthStats,
        last_30_days: thirtyDayStats
      };
      
    } catch (error) {
      console.error('Failed to get usage summary:', error);
      throw error;
    }
  }
  
  /**
   * Get user-specific usage statistics
   * @param {string} userId - User ID
   * @param {Object} options - Additional options
   * @returns {Object} User usage statistics
   */
  static async getUserUsageStats(userId, options = {}) {
    try {
      return await this.getUsageStats({ ...options, userId });
    } catch (error) {
      console.error('Failed to get user usage stats:', error);
      throw error;
    }
  }
  
  /**
   * Export usage data as CSV
   * @param {Object} options - Export options
   * @returns {string} CSV data
   */
  static async exportUsageData(options = {}) {
    try {
      const logs = await this.getUsageLogs({ ...options, limit: 10000 });
      
      if (!logs.data || logs.data.length === 0) {
        return 'No data to export';
      }
      
      // CSV headers
      const headers = [
        'Date',
        'User Email',
        'Endpoint',
        'Model',
        'Prompt Tokens',
        'Completion Tokens',
        'Total Tokens',
        'Duration (ms)',
        'Status',
        'Success',
        'Cost Estimate',
        'Thinking Mode'
      ];
      
      // Convert data to CSV rows
      const rows = logs.data.map(log => [
        new Date(log.request_timestamp).toLocaleString(),
        log.user_email || 'Unknown',
        log.endpoint,
        log.model,
        log.prompt_tokens,
        log.completion_tokens,
        log.total_tokens,
        log.request_duration_ms,
        log.status_code,
        log.success ? 'Yes' : 'No',
        `$${(log.cost_estimate || 0).toFixed(6)}`,
        log.thinking_mode ? 'Yes' : 'No'
      ]);
      
      // Combine headers and rows
      const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');
      
      return csvContent;
      
    } catch (error) {
      console.error('Failed to export usage data:', error);
      throw error;
    }
  }
  
  /**
   * Get model usage breakdown
   * @param {Object} options - Query options
   * @returns {Array} Model usage data
   */
  static async getModelUsageBreakdown(options = {}) {
    try {
      const stats = await this.getUsageStats(options);
      return stats.top_models || [];
    } catch (error) {
      console.error('Failed to get model usage breakdown:', error);
      throw error;
    }
  }
  
  /**
   * Get endpoint usage breakdown
   * @param {Object} options - Query options
   * @returns {Array} Endpoint usage data
   */
  static async getEndpointUsageBreakdown(options = {}) {
    try {
      const stats = await this.getUsageStats(options);
      return stats.top_endpoints || [];
    } catch (error) {
      console.error('Failed to get endpoint usage breakdown:', error);
      throw error;
    }
  }
  
  /**
   * Get daily usage trend
   * @param {Object} options - Query options
   * @returns {Array} Daily usage data
   */
  static async getDailyUsageTrend(options = {}) {
    try {
      const stats = await this.getUsageStats(options);
      return stats.daily_usage || [];
    } catch (error) {
      console.error('Failed to get daily usage trend:', error);
      throw error;
    }
  }
}
