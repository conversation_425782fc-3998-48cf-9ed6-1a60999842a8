/**
 * Authentication Debugger
 * Comprehensive debugging tools for authentication and performance issues
 */

export class AuthDebugger {
  static isEnabled = true; // Set to false in production
  static logs = [];
  static maxLogs = 100;
  static startTime = Date.now();

  static log(category, message, data = null) {
    if (!this.isEnabled) return;

    const timestamp = Date.now();
    const elapsed = timestamp - this.startTime;
    
    const logEntry = {
      timestamp,
      elapsed,
      category,
      message,
      data: data ? JSON.parse(JSON.stringify(data)) : null
    };

    this.logs.push(logEntry);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with color coding
    const colors = {
      AUTH: '#2563eb',
      PROFILE: '#7c3aed',
      STORAGE: '#059669',
      PERFORMANCE: '#dc2626',
      SUPABASE: '#3b82f6',
      ERROR: '#ef4444'
    };

    console.log(
      `%c[${category}] ${elapsed}ms: ${message}`,
      `color: ${colors[category] || '#6b7280'}; font-weight: bold;`,
      data || ''
    );
  }

  static startTimer(label) {
    if (!this.isEnabled) return null;
    const startTime = performance.now();
    this.log('PERFORMANCE', `Timer started: ${label}`);
    return {
      label,
      startTime,
      end: () => {
        const duration = performance.now() - startTime;
        this.log('PERFORMANCE', `Timer ended: ${label} - ${duration.toFixed(2)}ms`);
        return duration;
      }
    };
  }

  static checkStorageHealth() {
    if (!this.isEnabled) return;

    try {
      const localStorage = window.localStorage;
      const sessionStorage = window.sessionStorage;
      
      const localStorageSize = JSON.stringify(localStorage).length;
      const sessionStorageSize = JSON.stringify(sessionStorage).length;
      
      const storageInfo = {
        localStorage: {
          size: localStorageSize,
          items: localStorage.length,
          keys: Object.keys(localStorage)
        },
        sessionStorage: {
          size: sessionStorageSize,
          items: sessionStorage.length,
          keys: Object.keys(sessionStorage)
        }
      };

      this.log('STORAGE', 'Storage health check', storageInfo);

      // Check for potential issues
      if (localStorageSize > 5000000) { // 5MB
        this.log('STORAGE', 'WARNING: localStorage is getting large', { size: localStorageSize });
      }

      // Check for auth-related storage
      const authKeys = Object.keys(localStorage).filter(key => 
        key.includes('supabase') || key.includes('auth') || key.includes('session')
      );
      
      if (authKeys.length > 0) {
        this.log('STORAGE', 'Auth-related storage found', authKeys);
      }

    } catch (error) {
      this.log('ERROR', 'Storage health check failed', error);
    }
  }

  static async checkSupabaseConnection() {
    if (!this.isEnabled) return;

    const timer = this.startTimer('Supabase Connection Check');

    try {
      const { supabase } = await import('../supabase/client');

      this.log('SUPABASE', 'Testing connection', {
        url: supabase.supabaseUrl,
        keyPrefix: supabase.supabaseKey?.substring(0, 20) + '...'
      });

      // Test basic connection with timeout
      const connectionPromise = supabase.auth.getSession();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout after 5 seconds')), 5000)
      );

      const { data, error } = await Promise.race([connectionPromise, timeoutPromise]);

      timer.end();

      if (error) {
        this.log('SUPABASE', 'Connection error', {
          message: error.message,
          code: error.code,
          status: error.status
        });
        return false;
      } else {
        this.log('SUPABASE', 'Connection successful', {
          hasSession: !!data.session,
          sessionExpiry: data.session?.expires_at
        });
        return true;
      }
    } catch (error) {
      timer.end();
      this.log('ERROR', 'Supabase connection failed', {
        message: error.message,
        name: error.name,
        stack: error.stack?.split('\n')[0]
      });
      return false;
    }
  }

  static async profileLoadingTest(userId) {
    if (!this.isEnabled || !userId) return;

    const timer = this.startTimer('Profile Loading Test');
    
    try {
      const { UserProfileService } = await import('../services/userProfileService');
      
      const profile = await UserProfileService.getUserProfile(userId);
      
      timer.end();
      
      this.log('PROFILE', 'Profile loaded successfully', {
        userId,
        profile: profile ? 'Found' : 'Not found'
      });
      
      return profile;
    } catch (error) {
      timer.end();
      this.log('ERROR', 'Profile loading failed', { userId, error });
      return null;
    }
  }

  static detectInfiniteLoops() {
    if (!this.isEnabled) return;

    const recentLogs = this.logs.slice(-20); // Last 20 logs
    const categories = recentLogs.map(log => log.category);
    
    // Check for repeated patterns
    const authCount = categories.filter(cat => cat === 'AUTH').length;
    const profileCount = categories.filter(cat => cat === 'PROFILE').length;
    
    if (authCount > 10) {
      this.log('ERROR', 'Potential infinite loop detected in AUTH operations', { count: authCount });
    }
    
    if (profileCount > 10) {
      this.log('ERROR', 'Potential infinite loop detected in PROFILE operations', { count: profileCount });
    }
  }

  static generateReport() {
    if (!this.isEnabled) return null;

    const report = {
      totalLogs: this.logs.length,
      categories: {},
      errors: [],
      performance: [],
      timeline: this.logs.map(log => ({
        elapsed: log.elapsed,
        category: log.category,
        message: log.message
      }))
    };

    // Categorize logs
    this.logs.forEach(log => {
      if (!report.categories[log.category]) {
        report.categories[log.category] = 0;
      }
      report.categories[log.category]++;

      if (log.category === 'ERROR') {
        report.errors.push(log);
      }

      if (log.category === 'PERFORMANCE') {
        report.performance.push(log);
      }
    });

    return report;
  }

  static clearLogs() {
    this.logs = [];
    this.startTime = Date.now();
    this.log('AUTH', 'Debug logs cleared');
  }

  static exportLogs() {
    const report = this.generateReport();
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `auth-debug-${new Date().toISOString()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }
}

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.AuthDebugger = AuthDebugger;
  console.log('🔧 AuthDebugger available globally as window.AuthDebugger');
  console.log('🔧 Use AuthDebugger.generateReport() to see debug info');
  console.log('🔧 Use AuthDebugger.exportLogs() to download debug data');
}

export default AuthDebugger;
