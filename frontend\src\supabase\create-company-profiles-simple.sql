-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the company_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.company_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  company_name TEXT,
  industry TEXT,
  business_type TEXT,
  company_size TEXT,
  founded_year INTEGER,
  location TEXT,
  mission TEXT,
  vision TEXT,
  core_values TEXT,
  target_audience TEXT,
  target_market TEXT,
  unique_selling_points TEXT,
  unique_selling_proposition TEXT,
  competitive_advantages TEXT,
  competitors TEXT,
  challenges TEXT,
  current_challenges TEXT,
  business_goals TEXT,
  primary_products TEXT,
  brand_positioning TEXT,
  is_primary BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Remove the unique constraint on user_id to allow multiple companies per user
-- (This will be handled by the migration script below)

-- Add comment to the table
COMMENT ON TABLE public.company_profiles IS 'Stores company profile information for users - supports multiple companies per user';

-- Enable RLS
ALTER TABLE public.company_profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for users to select their own company profiles
CREATE POLICY IF NOT EXISTS select_own_company_profiles ON public.company_profiles
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy for users to insert their own company profiles
CREATE POLICY IF NOT EXISTS insert_own_company_profiles ON public.company_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policy for users to update their own company profiles
CREATE POLICY IF NOT EXISTS update_own_company_profiles ON public.company_profiles
  FOR UPDATE USING (auth.uid() = user_id);

-- Create policy for users to delete their own company profiles
CREATE POLICY IF NOT EXISTS delete_own_company_profiles ON public.company_profiles
  FOR DELETE USING (auth.uid() = user_id);

-- Add comment to the table
COMMENT ON TABLE public.company_profiles IS 'Stores company profile information for users';

-- Enable RLS
ALTER TABLE public.company_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for row-level security
DROP POLICY IF EXISTS select_own_company_profile ON public.company_profiles;
CREATE POLICY select_own_company_profile ON public.company_profiles
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS insert_own_company_profile ON public.company_profiles;
CREATE POLICY insert_own_company_profile ON public.company_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS update_own_company_profile ON public.company_profiles;
CREATE POLICY update_own_company_profile ON public.company_profiles
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS delete_own_company_profile ON public.company_profiles;
CREATE POLICY delete_own_company_profile ON public.company_profiles
  FOR DELETE USING (auth.uid() = user_id);

-- Create trigger function for updating the updated_at timestamp
CREATE OR REPLACE FUNCTION update_company_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS update_company_profiles_updated_at ON public.company_profiles;
CREATE TRIGGER update_company_profiles_updated_at
BEFORE UPDATE ON public.company_profiles
FOR EACH ROW
EXECUTE FUNCTION update_company_profiles_updated_at();

-- Grant access to the table
GRANT ALL ON public.company_profiles TO authenticated;
GRANT ALL ON public.company_profiles TO service_role;
