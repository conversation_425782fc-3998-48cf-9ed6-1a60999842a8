-- Simple API usage tracking table creation
-- Run this in your Supabase SQL editor if you encounter issues with the main file

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create api_usage_logs table
CREATE TABLE IF NOT EXISTS public.api_usage_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  user_email TEXT,
  endpoint TEXT NOT NULL,
  model TEXT NOT NULL,
  prompt_tokens INTEGER DEFAULT 0,
  completion_tokens INTEGER DEFAULT 0,
  total_tokens INTEGER DEFAULT 0,
  request_duration_ms INTEGER,
  status_code INTEGER NOT NULL,
  success BOOLEAN NOT NULL DEFAULT false,
  error_message TEXT,
  thinking_mode BOOLEAN DEFAULT false,
  request_timestamp TIMESTAMP WITH TIME ZONE DEFAULT now(),
  request_date DATE DEFAULT CURRENT_DATE,
  cost_estimate DECIMAL(10, 6) DEFAULT 0.00,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add comments
COMMENT ON TABLE public.api_usage_logs IS 'API usage tracking for monitoring client usage and costs';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_user_id ON public.api_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_user_email ON public.api_usage_logs(user_email);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_endpoint ON public.api_usage_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_model ON public.api_usage_logs(model);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_request_date ON public.api_usage_logs(request_date);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_request_timestamp ON public.api_usage_logs(request_timestamp);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_success ON public.api_usage_logs(success);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_user_date ON public.api_usage_logs(user_id, request_date);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_endpoint_date ON public.api_usage_logs(endpoint, request_date);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_model_date ON public.api_usage_logs(model, request_date);

-- Enable Row Level Security (RLS)
ALTER TABLE public.api_usage_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Policy for users to view their own usage logs
CREATE POLICY "Users can view own usage logs" ON public.api_usage_logs
  FOR SELECT USING (auth.uid() = user_id);

-- Policy for admins to view all usage logs
CREATE POLICY "Admins can view all usage logs" ON public.api_usage_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for system to insert usage logs (no user restriction for logging)
CREATE POLICY "System can insert usage logs" ON public.api_usage_logs
  FOR INSERT WITH CHECK (true);

-- Function to automatically set request_date from request_timestamp
CREATE OR REPLACE FUNCTION public.set_request_date()
RETURNS TRIGGER AS $$
BEGIN
  NEW.request_date := NEW.request_timestamp::date;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically set request_date on insert/update
CREATE TRIGGER set_api_usage_request_date
  BEFORE INSERT OR UPDATE ON public.api_usage_logs
  FOR EACH ROW EXECUTE FUNCTION public.set_request_date();

-- Grant necessary permissions
GRANT SELECT ON public.api_usage_logs TO authenticated;
GRANT INSERT ON public.api_usage_logs TO service_role;

-- Test the table by inserting a sample record
INSERT INTO public.api_usage_logs (
  user_email,
  endpoint,
  model,
  prompt_tokens,
  completion_tokens,
  total_tokens,
  request_duration_ms,
  status_code,
  success,
  thinking_mode,
  cost_estimate
) VALUES (
  '<EMAIL>',
  'openai',
  'gpt-4o',
  100,
  50,
  150,
  2500,
  200,
  true,
  false,
  0.00375
);

-- Verify the test record was inserted correctly
SELECT 
  id,
  user_email,
  endpoint,
  model,
  total_tokens,
  cost_estimate,
  request_date,
  request_timestamp
FROM public.api_usage_logs 
WHERE user_email = '<EMAIL>'
LIMIT 1;
