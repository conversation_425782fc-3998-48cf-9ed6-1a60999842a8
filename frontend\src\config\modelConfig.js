/**
 * LLM Model Configuration
 * 
 * This file manages which AI models are available for different clients.
 * You can enable/disable models for specific clients or globally.
 */

// All available models with their configurations
export const ALL_MODELS = {

  // OpenAI Models
  'gpt-4o': {
    value: 'gpt-4o',
    label: 'GPT-4o',
    provider: 'OpenAI',
    category: 'OpenAI',
    requiresApiKey: true,
    cost: 'high',
    description: 'Latest GPT-4 optimized model'
  },
  'gpt-4-turbo': {
    value: 'gpt-4-turbo',
    label: 'GPT-4 Turbo',
    provider: 'OpenAI',
    category: 'OpenAI',
    requiresApiKey: true,
    cost: 'high',
    description: 'Fast and capable GPT-4 variant'
  },
  'gpt-3.5-turbo': {
    value: 'gpt-3.5-turbo',
    label: 'GPT-3.5 Turbo',
    provider: 'OpenAI',
    category: 'OpenAI',
    requiresApiKey: true,
    cost: 'low',
    description: 'Fast and cost-effective model'
  },

  // Google Gemini Models
  'gemini-1.5-pro': {
    value: 'gemini-1.5-pro',
    label: 'Gemini 1.5 Pro',
    provider: 'Google',
    category: 'Google Gemini',
    requiresApiKey: false,
    cost: 'medium',
    description: 'Advanced multimodal capabilities'
  },
  'gemini-1.5-flash': {
    value: 'gemini-1.5-flash',
    label: 'Gemini 1.5 Flash',
    provider: 'Google',
    category: 'Google Gemini',
    requiresApiKey: false,
    cost: 'low',
    description: 'Fast and efficient model'
  },

  // DeepSeek Models
  'deepseek-chat': {
    value: 'deepseek-chat',
    label: 'DeepSeek Chat',
    provider: 'DeepSeek',
    category: 'DeepSeek',
    requiresApiKey: false,
    cost: 'low',
    description: 'General purpose chat model'
  }
};

// Default model availability for all clients
export const DEFAULT_MODEL_CONFIG = {
  // Standard models - available to all
  'gpt-4o': true,
  'gpt-4-turbo': true,
  'gpt-3.5-turbo': true,
  'gemini-1.5-pro': true,
  'gemini-1.5-flash': true,
  'deepseek-chat': true
};

// Client-specific model configurations
// Add entries here to customize model availability for specific clients
export const CLIENT_MODEL_CONFIGS = {
  // Admin gets all models (this is now handled by the database user_profiles table)
  // '<EMAIL>': {
  //   // All models enabled (empty config = all available)
  // },

  // Example: Basic tier client with limited models
  '<EMAIL>': {
    'gpt-4o': false,
    'gpt-4-turbo': false,
    // All other models use default config
  },

  // Example: Premium client with all models
  '<EMAIL>': {
    // All models enabled (uses default config)
  },

  // Example: Budget client with only free/low-cost models
  '<EMAIL>': {
    'gpt-4o': false,
    'gpt-4-turbo': false,
    'gemini-1.5-pro': false,
    // Only keep low-cost models enabled
  }
};

/**
 * Load client configurations from external JSON file
 * This allows for easier management of client access without code changes
 */
let externalClientConfigs = {};
try {
  // Note: In a production environment, you might want to load this from an API
  // For now, we'll use the static configuration above
  // You can replace this with an API call to load configurations dynamically
} catch (error) {
  console.warn('Could not load external client configurations:', error);
}

/**
 * Get available models for a specific client
 * @param {string} clientEmail - Client's email address (optional)
 * @param {Object} user - User object with email and other properties (optional)
 * @param {Object} userProfile - User profile with role, tier, and permissions (optional)
 * @returns {Object} - Grouped model options for dropdowns
 */
export function getAvailableModels(clientEmail = null, user = null, userProfile = null) {
  // Determine client identifier - prioritize userProfile, then user, then clientEmail
  let clientId = 'default';
  let clientConfig = {};

  if (userProfile) {
    // Use user profile for configuration
    clientId = userProfile.email || (user && user.email) || 'default';

    // Check if user has specific model permissions in their profile
    if (userProfile.permissions && userProfile.permissions.models) {
      clientConfig = userProfile.permissions.models;
    } else {
      // Use tier-based configuration
      const tierConfigs = {
        'free': {
          'gpt-4o': false,
          'gpt-4-turbo': false,
          'gemini-1.5-pro': false
        },
        'basic': {
          'gpt-4o': false,
          'gpt-4-turbo': true,
          'gemini-1.5-pro': true
        },
        'premium': {
          // All models available (empty config)
        }
      };

      clientConfig = tierConfigs[userProfile.tier] || tierConfigs['free'];
    }
  } else {
    // Fallback to old system
    clientId = clientEmail || (user && user.email) || 'default';
    clientConfig = CLIENT_MODEL_CONFIGS[clientId] || {};
  }

  // Merge with default config
  const finalConfig = { ...DEFAULT_MODEL_CONFIG, ...clientConfig };

  // Filter available models
  const availableModels = {};
  Object.entries(ALL_MODELS).forEach(([modelId, modelInfo]) => {
    if (finalConfig[modelId] !== false) {
      availableModels[modelId] = modelInfo;
    }
  });

  // Group models by category for dropdown display
  const groupedModels = {};
  Object.values(availableModels).forEach(model => {
    if (!groupedModels[model.category]) {
      groupedModels[model.category] = [];
    }
    groupedModels[model.category].push({
      value: model.value,
      label: model.label
    });
  });

  return groupedModels;
}

/**
 * Check if a specific model is available for a client
 * @param {string} modelId - Model identifier
 * @param {string} clientEmail - Client's email address (optional)
 * @param {Object} user - User object (optional)
 * @param {Object} userProfile - User profile with role, tier, and permissions (optional)
 * @returns {boolean} - Whether the model is available
 */
export function isModelAvailable(modelId, clientEmail = null, user = null, userProfile = null) {
  if (!ALL_MODELS[modelId]) return false;

  const availableModels = getAvailableModels(clientEmail, user, userProfile);

  // Check if model exists in any category
  for (const category of Object.values(availableModels)) {
    if (category.some(model => model.value === modelId)) {
      return true;
    }
  }

  return false;
}

/**
 * Get model information
 * @param {string} modelId - Model identifier
 * @returns {Object|null} - Model information or null if not found
 */
export function getModelInfo(modelId) {
  return ALL_MODELS[modelId] || null;
}

export default {
  ALL_MODELS,
  DEFAULT_MODEL_CONFIG,
  CLIENT_MODEL_CONFIGS,
  getAvailableModels,
  isModelAvailable,
  getModelInfo
};
