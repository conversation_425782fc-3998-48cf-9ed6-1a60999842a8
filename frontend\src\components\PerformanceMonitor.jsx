import React, { useState, useEffect } from 'react';
import { AuthDebugger } from '../utils/authDebugger';
import { ConnectionTest } from '../utils/connectionTest';
import { useAuth } from '../context/AuthContext';

/**
 * Performance Monitor Component
 * Shows real-time debugging information for authentication and performance issues
 */
function PerformanceMonitor() {
  const [isVisible, setIsVisible] = useState(false);
  const [debugReport, setDebugReport] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const { user, userProfile, loading, fallbackMode, StorageManager } = useAuth();

  // Auto-refresh debug report
  useEffect(() => {
    if (!autoRefresh || !isVisible) return;

    const interval = setInterval(() => {
      const report = AuthDebugger.generateReport();
      setDebugReport(report);
      AuthDebugger.detectInfiniteLoops();
    }, 2000);

    return () => clearInterval(interval);
  }, [autoRefresh, isVisible]);

  // Initial load
  useEffect(() => {
    if (isVisible && !debugReport) {
      const report = AuthDebugger.generateReport();
      setDebugReport(report);
    }
  }, [isVisible]);

  // Keyboard shortcut to toggle (Ctrl+Shift+D)
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        e.preventDefault();
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const clearStorage = () => {
    if (window.confirm('Clear all browser storage? This will log you out.')) {
      StorageManager.clearAll();
      window.location.reload();
    }
  };

  const clearAuthStorage = () => {
    if (window.confirm('Clear authentication storage? This will log you out.')) {
      StorageManager.clearUserSpecificData();
      window.location.reload();
    }
  };

  const runHealthCheck = async () => {
    AuthDebugger.log('PERFORMANCE', 'Running health check');
    AuthDebugger.checkStorageHealth();
    await AuthDebugger.checkSupabaseConnection();

    // Run connection test
    const connectionResults = await ConnectionTest.runQuickTest();
    AuthDebugger.log('PERFORMANCE', 'Connection test completed', connectionResults.summary);

    if (user && !user.fallback) {
      await AuthDebugger.profileLoadingTest(user.id);
    }

    const report = AuthDebugger.generateReport();
    setDebugReport(report);
  };

  const runDiagnosis = async () => {
    const diagnosis = await ConnectionTest.diagnoseSlowLoading();
    AuthDebugger.log('PERFORMANCE', 'Diagnosis completed', diagnosis);

    const report = AuthDebugger.generateReport();
    setDebugReport(report);
  };

  if (!isVisible) {
    return (
      <div 
        className="fixed bottom-4 right-4 bg-red-500 text-white px-3 py-1 rounded text-sm cursor-pointer z-50"
        onClick={() => setIsVisible(true)}
        title="Click to open Performance Monitor (or press Ctrl+Shift+D)"
      >
        🔧 Debug
      </div>
    );
  }

  return (
    <div className="fixed top-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md max-h-96 overflow-y-auto z-50 text-sm">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-bold text-gray-800">Performance Monitor</h3>
        <button 
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      {/* Auth Status */}
      <div className="mb-3 p-2 bg-gray-50 rounded">
        <h4 className="font-semibold mb-1">Auth Status</h4>
        <div className="text-xs space-y-1">
          <div>User: {user ? (user.fallback ? 'Fallback' : user.email) : 'None'}</div>
          <div>Profile: {userProfile ? userProfile.role : 'None'}</div>
          <div>Loading: {loading ? 'Yes' : 'No'}</div>
          <div>Fallback: {fallbackMode ? 'Yes' : 'No'}</div>
        </div>
      </div>

      {/* Controls */}
      <div className="mb-3 space-y-2">
        <div className="flex space-x-2">
          <button
            onClick={runHealthCheck}
            className="px-2 py-1 bg-blue-500 text-white rounded text-xs"
          >
            Health Check
          </button>
          <button
            onClick={runDiagnosis}
            className="px-2 py-1 bg-orange-500 text-white rounded text-xs"
          >
            Diagnose
          </button>
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`px-2 py-1 rounded text-xs ${autoRefresh ? 'bg-green-500 text-white' : 'bg-gray-300'}`}
          >
            Auto Refresh
          </button>
        </div>
        
        <div className="flex space-x-2">
          <button 
            onClick={clearAuthStorage}
            className="px-2 py-1 bg-yellow-500 text-white rounded text-xs"
          >
            Clear Auth
          </button>
          <button 
            onClick={clearStorage}
            className="px-2 py-1 bg-red-500 text-white rounded text-xs"
          >
            Clear All
          </button>
        </div>

        <div className="flex space-x-2">
          <button 
            onClick={() => AuthDebugger.exportLogs()}
            className="px-2 py-1 bg-purple-500 text-white rounded text-xs"
          >
            Export Logs
          </button>
          <button 
            onClick={() => {
              AuthDebugger.clearLogs();
              setDebugReport(null);
            }}
            className="px-2 py-1 bg-gray-500 text-white rounded text-xs"
          >
            Clear Logs
          </button>
        </div>
      </div>

      {/* Debug Report */}
      {debugReport && (
        <div className="space-y-2">
          <div className="p-2 bg-gray-50 rounded">
            <h4 className="font-semibold mb-1">Log Summary</h4>
            <div className="text-xs space-y-1">
              <div>Total Logs: {debugReport.totalLogs}</div>
              {Object.entries(debugReport.categories).map(([category, count]) => (
                <div key={category} className={count > 10 ? 'text-red-600 font-bold' : ''}>
                  {category}: {count}
                </div>
              ))}
            </div>
          </div>

          {debugReport.errors.length > 0 && (
            <div className="p-2 bg-red-50 rounded">
              <h4 className="font-semibold mb-1 text-red-700">Recent Errors</h4>
              <div className="text-xs space-y-1">
                {debugReport.errors.slice(-3).map((error, index) => (
                  <div key={index} className="text-red-600">
                    {error.elapsed}ms: {error.message}
                  </div>
                ))}
              </div>
            </div>
          )}

          {debugReport.performance.length > 0 && (
            <div className="p-2 bg-blue-50 rounded">
              <h4 className="font-semibold mb-1 text-blue-700">Performance</h4>
              <div className="text-xs space-y-1">
                {debugReport.performance.slice(-3).map((perf, index) => (
                  <div key={index} className="text-blue-600">
                    {perf.elapsed}ms: {perf.message}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="mt-3 text-xs text-gray-500">
        Press Ctrl+Shift+D to toggle
      </div>
    </div>
  );
}

export default PerformanceMonitor;
