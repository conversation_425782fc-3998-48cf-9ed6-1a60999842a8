import React from 'react';

/**
 * PartnershipJourneySection Component
 * 
 * A reusable component that displays "The Partnership Journey" section
 * with a structured approach to partnership development from initial contact to active collaboration.
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Section title (default: "The Partnership Journey")
 * @param {string} props.description - Section description
 * @param {Array} props.steps - Array of journey step objects
 * @param {string} props.steps[].number - Step number
 * @param {string} props.steps[].title - Step title
 * @param {string} props.steps[].description - Step description
 * @param {string} props.themeColor - Theme color for styling (default: 'purple')
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.columns - Number of columns in the grid (default: 4)
 */
function PartnershipJourneySection({
  title = "The Partnership Journey",
  description = "Our structured approach ensures successful partnership development from initial contact to active collaboration:",
  steps = [
    {
      number: "1",
      title: "Discovery",
      description: "Initial consultation and needs assessment"
    },
    {
      number: "2",
      title: "Evaluation",
      description: "Mutual fit assessment and opportunity analysis"
    },
    {
      number: "3",
      title: "Agreement",
      description: "Contract negotiation and partnership terms"
    },
    {
      number: "4",
      title: "Activation",
      description: "Onboarding, training, and go-to-market execution"
    }
  ],
  themeColor = 'purple',
  className = '',
  columns = 4
}) {
  // Define color schemes for different themes
  const colorSchemes = {
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      titleText: 'text-red-800',
      stepBg: 'bg-blue-400',
      stepTitleText: 'text-red-700'
    },
    amber: {
      bg: 'bg-amber-50',
      border: 'border-amber-200',
      titleText: 'text-amber-800',
      stepBg: 'bg-blue-600',
      stepTitleText: 'text-amber-700'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      titleText: 'text-blue-600',
      stepBg: 'bg-blue-600',
      stepTitleText: 'text-blue-700'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      titleText: 'text-green-800',
      stepBg: 'bg-blue-600',
      stepTitleText: 'text-green-700'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      titleText: 'text-purple-800',
      stepBg: 'bg-blue-600',
      stepTitleText: 'text-purple-700'
    }
  };

  const colors = colorSchemes[themeColor] || colorSchemes.purple;

  // Generate grid column classes
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-6'
  };

  const gridClass = gridCols[columns] || gridCols[4];

  return (
    <div className={`mb-8 p-6 ${colors.bg} rounded-lg border ${colors.border} ${className}`}>
      <h2 className={`raleway-title-h2 mb-4 ${colors.titleText}`}>{title}</h2>
      <p className="body-text mb-4">
        {description}
      </p>
      <div className={`grid ${gridClass} gap-4`}>
        {steps.map((step, index) => (
          <div key={index} className="text-center p-4 bg-white rounded border">
            <div className={`w-7 h-7 ${colors.stepBg} text-white rounded-full flex items-center justify-center mx-auto mb-3 font-bold text-sm`}>
              {step.number}
            </div>
            <h4 className={`raleway-title-h4 mb-2 ${colors.stepTitleText}`}>{step.title}</h4>
            <p className="body-text">{step.description}</p>
          </div>
        ))}
      </div>
    </div>
  );
}

export default PartnershipJourneySection;
