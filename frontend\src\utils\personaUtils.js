/**
 * Persona Utilities
 * Handles loading and processing of AI personas with company profile integration
 */

import yaml from 'js-yaml';
import { supabase } from '../supabase/client.js';

// Cache for loaded data
let personasCache = null;
let companyProfileCache = null;

/**
 * Load personas from YAML file
 */
export async function loadPersonas(forceReload = false) {
  if (personasCache && !forceReload) return personasCache;

  try {
    // Add cache busting parameter to force reload
    const cacheBuster = forceReload ? `?t=${Date.now()}` : '';
    const response = await fetch(`/config/personas.yaml${cacheBuster}`);
    const yamlText = await response.text();
    personasCache = yaml.load(yamlText);
    console.log('Personas loaded:', forceReload ? '(force reload)' : '(cached)');
    return personasCache;
  } catch (error) {
    console.error('Error loading personas:', error);
    return {};
  }
}

/**
 * Clear personas cache - useful for development
 */
export function clearPersonasCache() {
  personasCache = null;
  console.log('Personas cache cleared');
}

/**
 * Load company profile from database
 */
export async function loadCompanyProfile(userId = null) {
  try {
    // Get current user if not provided
    if (!userId) {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.warn('No authenticated user found');
        return {};
      }
      userId = user.id;
    }

    // Get the user's single company profile
    const { data, error } = await supabase
      .from('company_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error loading company profile:', error);
      return {};
    }

    if (!data) {
      console.warn('No company profile found');
      return {};
    }

    // Transform database data to match expected format
    const companyProfile = {
      placeholders: {
        companyName: data.company_name || 'Your Company',
        industry: data.industry || 'your industry',
        businessType: data.business_type || 'business',
        targetMarket: data.target_market || data.target_audience || 'your target market',
        businessGoals: data.business_goals || 'your business goals',
        primaryFocus: data.primary_products || 'your products and services',
        keyDifferentiator: data.unique_selling_proposition || data.unique_selling_points || 'your unique value',
        marketPosition: data.brand_positioning || 'your market position'
      },
      // Include full profile data for reference
      fullProfile: data
    };

    return companyProfile;
  } catch (error) {
    console.error('Error loading company profile:', error);
    return {};
  }
}

/**
 * Replace placeholders in text with company profile data
 */
export function replacePlaceholders(text, companyProfile) {
  if (!text || !companyProfile?.placeholders) return text;
  
  let processedText = text;
  
  // Replace each placeholder with actual company data
  Object.entries(companyProfile.placeholders).forEach(([key, value]) => {
    const placeholder = `{${key}}`;
    processedText = processedText.replace(new RegExp(placeholder, 'g'), value);
  });
  
  return processedText;
}

/**
 * Get persona for a specific page with company profile integration
 */
export async function getPersonaForPage(pageId, userId = null) {
  try {
    const [personas, companyProfile] = await Promise.all([
      loadPersonas(),
      loadCompanyProfile(userId)
    ]);

    const persona = personas[pageId];
    if (!persona) {
      console.warn(`No persona found for page: ${pageId}`);
      return null;
    }

    // Process persona with company profile data
    const processedPersona = {
      ...persona,
      introduction: replacePlaceholders(persona.introduction, companyProfile),
      context: replacePlaceholders(persona.context, companyProfile),
      companyProfile: companyProfile // Include full company profile for reference
    };

    return processedPersona;
  } catch (error) {
    console.error('Error getting persona for page:', pageId, error);
    return null;
  }
}

/**
 * Get formatted introduction message for a persona
 */
export function getPersonaIntroduction(persona) {
  if (!persona?.introduction) return null;
  
  return {
    name: persona.name,
    role: persona.role,
    message: persona.introduction,
    expertise: persona.expertise || []
  };
}

/**
 * Get context prompt for AI requests
 */
export function getPersonaContext(persona) {
  if (!persona?.context) return '';
  
  return persona.context;
}

/**
 * Format persona for display
 */
export function formatPersonaDisplay(persona) {
  if (!persona) return null;
  
  return {
    title: `${persona.name} - ${persona.role}`,
    description: `Your ${persona.role.toLowerCase()} specializing in ${persona.expertise?.join(', ') || 'business strategy'}.`,
    introduction: getPersonaIntroduction(persona),
    contextPrompt: getPersonaContext(persona)
  };
}

/**
 * Get page ID from current route
 */
export function getPageIdFromRoute(pathname) {
  // Map routes to persona page IDs
  const routeMapping = {
    '/': 'home',
    '/home': 'home',
    '/dashboard': 'home',
    '/market-research': 'market-research',
    '/competition-analysis': 'competition-analysis',
    '/client-acquisition': 'client-acquisition',
    '/customer-retention': 'customer-retention',
    '/partnerships': 'partnerships',
    '/strategy': 'strategy'
  };
  
  return routeMapping[pathname] || 'home';
}



/**
 * Hook for React components to use personas
 * Note: This would need React import in a component that uses it
 */
export function createPersonaHook() {
  return function usePersona(pageId) {
    // This would be implemented in a React component with proper React imports
    // For now, components should use getPersonaForPage directly
    console.warn('usePersona hook not implemented - use getPersonaForPage directly');
    return { persona: null, loading: false, error: null };
  };
}

export default {
  loadPersonas,
  loadCompanyProfile,
  replacePlaceholders,
  getPersonaForPage,
  getPersonaIntroduction,
  getPersonaContext,
  formatPersonaDisplay,
  getPageIdFromRoute,
  createPersonaHook,
  clearPersonasCache
};
