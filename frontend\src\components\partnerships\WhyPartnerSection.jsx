import React from 'react';

/**
 * WhyPartnerSection Component
 * 
 * A reusable component that displays the "Why Partner With Us?" section
 * with compelling value propositions and benefits for potential partners.
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Section title (default: "Why Partner With Us?")
 * @param {string} props.description - Section description
 * @param {Array} props.benefits - Array of benefit objects
 * @param {string} props.benefits[].title - Benefit title
 * @param {string} props.benefits[].description - Benefit description
 * @param {string} props.themeColor - Theme color for styling (default: 'purple')
 * @param {string} props.className - Additional CSS classes
 */
function WhyPartnerSection({
  title = "Why Partner With Us?",
  description = "Partnering with our market intelligence platform opens doors to unprecedented opportunities for growth and innovation. We offer compelling value propositions that benefit both parties through:",
  benefits = [
    {
      title: "Market Expansion",
      description: "Access new customer segments and geographic markets"
    },
    {
      title: "Enhanced Offerings",
      description: "Complement your services with cutting-edge market intelligence"
    },
    {
      title: "Revenue Growth",
      description: "Create new revenue streams through partnership models"
    },
    {
      title: "Competitive Advantage",
      description: "Leverage our expertise to differentiate your solutions"
    },
    {
      title: "Innovation Acceleration",
      description: "Collaborate on next-generation market research tools"
    }
  ],
  themeColor = 'purple',
  className = ''
}) {
  // Define color schemes for different themes
  const colorSchemes = {
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      titleText: 'text-red-800'
    },
    amber: {
      bg: 'bg-amber-50',
      border: 'border-amber-200',
      titleText: 'text-amber-800'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      titleText: 'text-blue-800'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      titleText: 'text-green-800'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      titleText: 'text-purple-800'
    }
  };

  const colors = colorSchemes[themeColor] || colorSchemes.purple;

  return (
    <div className={`mb-8 p-6 ${colors.bg} rounded-lg border ${colors.border} ${className}`}>
      <h2 className={`raleway-title-h2 mb-4 ${colors.titleText}`}>{title}</h2>
      <p className="body-text mb-4">
        {description}
      </p>
      <ul className="body-text space-y-2 ml-6">
        {benefits.map((benefit, index) => (
          <li key={index}>
            • <strong>{benefit.title}:</strong> {benefit.description}
          </li>
        ))}
      </ul>
    </div>
  );
}

export default WhyPartnerSection;
