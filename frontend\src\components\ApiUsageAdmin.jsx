import React, { useState, useEffect } from 'react';
import { ApiUsageService } from '../services/apiUsageService';

/**
 * API Usage Admin Component
 * Displays API usage statistics and logs for admin monitoring
 */
export default function ApiUsageAdmin() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Overview data
  const [summary, setSummary] = useState(null);
  
  // Logs data
  const [logs, setLogs] = useState([]);
  const [logsLoading, setLogsLoading] = useState(false);
  const [pagination, setPagination] = useState({
    limit: 50,
    offset: 0,
    total: 0
  });
  
  // Filters
  const [filters, setFilters] = useState({
    startDate: '',
    endDate: '',
    userId: '',
    endpoint: '',
    model: ''
  });
  
  // Statistics data
  const [stats, setStats] = useState(null);
  const [statsLoading, setStatsLoading] = useState(false);
  
  useEffect(() => {
    loadSummary();
  }, []);
  
  useEffect(() => {
    if (activeTab === 'logs') {
      loadLogs();
    } else if (activeTab === 'statistics') {
      loadStats();
    }
  }, [activeTab, pagination.offset, filters]);
  
  const loadSummary = async () => {
    try {
      setLoading(true);
      const summaryData = await ApiUsageService.getUsageSummary();
      setSummary(summaryData);
    } catch (err) {
      setError('Failed to load usage summary');
      console.error('Error loading summary:', err);
    } finally {
      setLoading(false);
    }
  };
  
  const loadLogs = async () => {
    try {
      setLogsLoading(true);
      const logsData = await ApiUsageService.getUsageLogs({
        limit: pagination.limit,
        offset: pagination.offset,
        ...filters
      });
      setLogs(logsData.data);
      setPagination(prev => ({ ...prev, total: logsData.count }));
    } catch (err) {
      setError('Failed to load usage logs');
      console.error('Error loading logs:', err);
    } finally {
      setLogsLoading(false);
    }
  };
  
  const loadStats = async () => {
    try {
      setStatsLoading(true);
      const statsData = await ApiUsageService.getUsageStats(filters);
      setStats(statsData);
    } catch (err) {
      setError('Failed to load usage statistics');
      console.error('Error loading stats:', err);
    } finally {
      setStatsLoading(false);
    }
  };
  
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, offset: 0 })); // Reset pagination
  };
  
  const handleExport = async () => {
    try {
      const csvData = await ApiUsageService.exportUsageData(filters);
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `api-usage-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('Failed to export data');
      console.error('Error exporting:', err);
    }
  };
  
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 6
    }).format(amount || 0);
  };
  
  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num || 0);
  };
  
  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'logs', name: 'Usage Logs', icon: '📋' },
    { id: 'statistics', name: 'Statistics', icon: '📈' }
  ];
  
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        <span className="ml-2">Loading API usage data...</span>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-red-800 font-medium">Error</h3>
        <p className="text-red-600">{error}</p>
        <button
          onClick={() => {
            setError(null);
            loadSummary();
          }}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }
  
  const renderOverview = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Today */}
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Today</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Requests:</span>
              <span className="font-medium">{formatNumber(summary?.today?.total_requests)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Tokens:</span>
              <span className="font-medium">{formatNumber(summary?.today?.total_tokens)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Cost:</span>
              <span className="font-medium">{formatCurrency(summary?.today?.total_cost)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Success Rate:</span>
              <span className="font-medium">
                {summary?.today?.total_requests > 0 
                  ? Math.round((summary.today.successful_requests / summary.today.total_requests) * 100)
                  : 0}%
              </span>
            </div>
          </div>
        </div>
        
        {/* This Month */}
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">This Month</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Requests:</span>
              <span className="font-medium">{formatNumber(summary?.this_month?.total_requests)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Tokens:</span>
              <span className="font-medium">{formatNumber(summary?.this_month?.total_tokens)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Cost:</span>
              <span className="font-medium">{formatCurrency(summary?.this_month?.total_cost)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Users:</span>
              <span className="font-medium">{formatNumber(summary?.this_month?.unique_users)}</span>
            </div>
          </div>
        </div>
        
        {/* Last 30 Days */}
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Last 30 Days</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Requests:</span>
              <span className="font-medium">{formatNumber(summary?.last_30_days?.total_requests)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Tokens:</span>
              <span className="font-medium">{formatNumber(summary?.last_30_days?.total_tokens)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Cost:</span>
              <span className="font-medium">{formatCurrency(summary?.last_30_days?.total_cost)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Users:</span>
              <span className="font-medium">{formatNumber(summary?.last_30_days?.unique_users)}</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Top Models and Endpoints */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Top Models */}
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Top Models (Last 30 Days)</h3>
          <div className="space-y-3">
            {summary?.last_30_days?.top_models?.slice(0, 5).map((model, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-gray-700">{model.model}</span>
                <div className="text-right">
                  <div className="text-sm font-medium">{formatNumber(model.requests)} requests</div>
                  <div className="text-xs text-gray-500">{formatCurrency(model.cost)}</div>
                </div>
              </div>
            )) || <p className="text-gray-500">No data available</p>}
          </div>
        </div>
        
        {/* Top Endpoints */}
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Top Endpoints (Last 30 Days)</h3>
          <div className="space-y-3">
            {summary?.last_30_days?.top_endpoints?.slice(0, 5).map((endpoint, index) => (
              <div key={index} className="flex justify-between items-center">
                <span className="text-gray-700 capitalize">{endpoint.endpoint}</span>
                <div className="text-right">
                  <div className="text-sm font-medium">{formatNumber(endpoint.requests)} requests</div>
                  <div className="text-xs text-gray-500">{formatCurrency(endpoint.cost)}</div>
                </div>
              </div>
            )) || <p className="text-gray-500">No data available</p>}
          </div>
        </div>
      </div>
    </div>
  );
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="raleway-title-h2 mb-4">API Usage Monitoring</h2>
        <p className="body-text mb-6">
          Monitor API usage, costs, and performance across all clients and models.
        </p>
        
        {/* Tabs */}
        <div className="flex space-x-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </div>
      </div>
      
      {/* Content */}
      <div className="bg-white rounded-lg shadow-md">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'logs' && (
          <div className="p-6">
            <p className="text-gray-600">Usage logs functionality will be implemented next...</p>
          </div>
        )}
        {activeTab === 'statistics' && (
          <div className="p-6">
            <p className="text-gray-600">Statistics functionality will be implemented next...</p>
          </div>
        )}
      </div>
    </div>
  );
}
