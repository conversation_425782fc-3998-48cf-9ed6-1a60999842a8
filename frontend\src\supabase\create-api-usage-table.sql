-- Create API usage tracking table for monitoring client API usage
-- Run this in your Supabase SQL editor

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create api_usage_logs table
CREATE TABLE IF NOT EXISTS public.api_usage_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  user_email TEXT,
  endpoint TEXT NOT NULL,
  model TEXT NOT NULL,
  prompt_tokens INTEGER DEFAULT 0,
  completion_tokens INTEGER DEFAULT 0,
  total_tokens INTEGER DEFAULT 0,
  request_duration_ms INTEGER,
  status_code INTEGER NOT NULL,
  success BOOLEAN NOT NULL DEFAULT false,
  error_message TEXT,
  thinking_mode BOOLEAN DEFAULT false,
  request_timestamp TIMESTAMP WITH TIME ZONE DEFAULT now(),
  request_date DATE GENERATED ALWAYS AS (request_timestamp::date) STORED,
  cost_estimate DECIMAL(10, 6) DEFAULT 0.00,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add comments
COMMENT ON TABLE public.api_usage_logs IS 'API usage tracking for monitoring client usage and costs';
COMMENT ON COLUMN public.api_usage_logs.user_id IS 'Reference to the user who made the request';
COMMENT ON COLUMN public.api_usage_logs.user_email IS 'Email of the user (for easier querying)';
COMMENT ON COLUMN public.api_usage_logs.endpoint IS 'API endpoint called (e.g., openai, gemini, anthropic)';
COMMENT ON COLUMN public.api_usage_logs.model IS 'Model used for the request';
COMMENT ON COLUMN public.api_usage_logs.prompt_tokens IS 'Number of tokens in the prompt';
COMMENT ON COLUMN public.api_usage_logs.completion_tokens IS 'Number of tokens in the completion';
COMMENT ON COLUMN public.api_usage_logs.total_tokens IS 'Total tokens used';
COMMENT ON COLUMN public.api_usage_logs.request_duration_ms IS 'Request duration in milliseconds';
COMMENT ON COLUMN public.api_usage_logs.status_code IS 'HTTP status code of the response';
COMMENT ON COLUMN public.api_usage_logs.success IS 'Whether the request was successful';
COMMENT ON COLUMN public.api_usage_logs.error_message IS 'Error message if request failed';
COMMENT ON COLUMN public.api_usage_logs.thinking_mode IS 'Whether thinking mode was enabled';
COMMENT ON COLUMN public.api_usage_logs.request_date IS 'Date of the request (for easier aggregation)';
COMMENT ON COLUMN public.api_usage_logs.cost_estimate IS 'Estimated cost of the request in USD';
COMMENT ON COLUMN public.api_usage_logs.metadata IS 'Additional metadata about the request';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_user_id ON public.api_usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_user_email ON public.api_usage_logs(user_email);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_endpoint ON public.api_usage_logs(endpoint);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_model ON public.api_usage_logs(model);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_request_date ON public.api_usage_logs(request_date);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_request_timestamp ON public.api_usage_logs(request_timestamp);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_success ON public.api_usage_logs(success);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_user_date ON public.api_usage_logs(user_id, request_date);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_endpoint_date ON public.api_usage_logs(endpoint, request_date);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_model_date ON public.api_usage_logs(model, request_date);

-- Enable Row Level Security (RLS)
ALTER TABLE public.api_usage_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Policy for users to view their own usage logs
CREATE POLICY "Users can view own usage logs" ON public.api_usage_logs
  FOR SELECT USING (auth.uid() = user_id);

-- Policy for admins to view all usage logs
CREATE POLICY "Admins can view all usage logs" ON public.api_usage_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Policy for system to insert usage logs (no user restriction for logging)
CREATE POLICY "System can insert usage logs" ON public.api_usage_logs
  FOR INSERT WITH CHECK (true);

-- Function to update cost estimates based on model pricing
CREATE OR REPLACE FUNCTION public.calculate_api_cost(
  p_model TEXT,
  p_prompt_tokens INTEGER,
  p_completion_tokens INTEGER
) RETURNS DECIMAL(10, 6) AS $$
DECLARE
  prompt_cost DECIMAL(10, 6) := 0.00;
  completion_cost DECIMAL(10, 6) := 0.00;
  total_cost DECIMAL(10, 6) := 0.00;
BEGIN
  -- OpenAI pricing (per 1K tokens)
  IF p_model LIKE 'gpt-4o%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.0025;
    completion_cost := (p_completion_tokens / 1000.0) * 0.01;
  ELSIF p_model LIKE 'gpt-4%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.03;
    completion_cost := (p_completion_tokens / 1000.0) * 0.06;
  ELSIF p_model LIKE 'gpt-3.5%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.0015;
    completion_cost := (p_completion_tokens / 1000.0) * 0.002;
  ELSIF p_model LIKE 'o1-preview%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.015;
    completion_cost := (p_completion_tokens / 1000.0) * 0.06;
  ELSIF p_model LIKE 'o1-mini%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.003;
    completion_cost := (p_completion_tokens / 1000.0) * 0.012;
  -- Anthropic pricing (per 1K tokens)
  ELSIF p_model LIKE 'claude-3-opus%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.015;
    completion_cost := (p_completion_tokens / 1000.0) * 0.075;
  ELSIF p_model LIKE 'claude-3-sonnet%' OR p_model LIKE 'claude-3.5-sonnet%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.003;
    completion_cost := (p_completion_tokens / 1000.0) * 0.015;
  ELSIF p_model LIKE 'claude-3-haiku%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.00025;
    completion_cost := (p_completion_tokens / 1000.0) * 0.00125;
  -- Google Gemini pricing (per 1K tokens)
  ELSIF p_model LIKE 'gemini-1.5-pro%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.00125;
    completion_cost := (p_completion_tokens / 1000.0) * 0.005;
  ELSIF p_model LIKE 'gemini-1.5-flash%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.000075;
    completion_cost := (p_completion_tokens / 1000.0) * 0.0003;
  -- DeepSeek and Groq are typically much cheaper or free
  ELSIF p_model LIKE 'deepseek%' THEN
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.00014;
    completion_cost := (p_completion_tokens / 1000.0) * 0.00028;
  ELSIF p_model LIKE 'llama%' OR p_model LIKE 'mixtral%' THEN
    -- Groq pricing
    prompt_cost := (p_prompt_tokens / 1000.0) * 0.0001;
    completion_cost := (p_completion_tokens / 1000.0) * 0.0001;
  END IF;
  
  total_cost := prompt_cost + completion_cost;
  RETURN total_cost;
END;
$$ LANGUAGE plpgsql;

-- Function to get usage statistics
CREATE OR REPLACE FUNCTION public.get_api_usage_stats(
  p_start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
  p_end_date DATE DEFAULT CURRENT_DATE,
  p_user_id UUID DEFAULT NULL
) RETURNS TABLE (
  total_requests BIGINT,
  successful_requests BIGINT,
  failed_requests BIGINT,
  total_tokens BIGINT,
  total_cost DECIMAL(10, 6),
  unique_users BIGINT,
  top_models JSONB,
  top_endpoints JSONB,
  daily_usage JSONB
) AS $$
BEGIN
  RETURN QUERY
  WITH base_data AS (
    SELECT *
    FROM public.api_usage_logs
    WHERE request_date BETWEEN p_start_date AND p_end_date
      AND (p_user_id IS NULL OR user_id = p_user_id)
  ),
  aggregated AS (
    SELECT
      COUNT(*) as total_requests,
      COUNT(*) FILTER (WHERE success = true) as successful_requests,
      COUNT(*) FILTER (WHERE success = false) as failed_requests,
      COALESCE(SUM(total_tokens), 0) as total_tokens,
      COALESCE(SUM(cost_estimate), 0) as total_cost,
      COUNT(DISTINCT user_id) as unique_users
    FROM base_data
  ),
  model_stats AS (
    SELECT jsonb_agg(
      jsonb_build_object(
        'model', model,
        'requests', request_count,
        'tokens', total_tokens,
        'cost', total_cost
      ) ORDER BY request_count DESC
    ) as top_models
    FROM (
      SELECT 
        model,
        COUNT(*) as request_count,
        SUM(total_tokens) as total_tokens,
        SUM(cost_estimate) as total_cost
      FROM base_data
      GROUP BY model
      ORDER BY request_count DESC
      LIMIT 10
    ) t
  ),
  endpoint_stats AS (
    SELECT jsonb_agg(
      jsonb_build_object(
        'endpoint', endpoint,
        'requests', request_count,
        'tokens', total_tokens,
        'cost', total_cost
      ) ORDER BY request_count DESC
    ) as top_endpoints
    FROM (
      SELECT 
        endpoint,
        COUNT(*) as request_count,
        SUM(total_tokens) as total_tokens,
        SUM(cost_estimate) as total_cost
      FROM base_data
      GROUP BY endpoint
      ORDER BY request_count DESC
      LIMIT 10
    ) t
  ),
  daily_stats AS (
    SELECT jsonb_agg(
      jsonb_build_object(
        'date', request_date,
        'requests', request_count,
        'tokens', total_tokens,
        'cost', total_cost
      ) ORDER BY request_date
    ) as daily_usage
    FROM (
      SELECT 
        request_date,
        COUNT(*) as request_count,
        SUM(total_tokens) as total_tokens,
        SUM(cost_estimate) as total_cost
      FROM base_data
      GROUP BY request_date
      ORDER BY request_date
    ) t
  )
  SELECT 
    a.total_requests,
    a.successful_requests,
    a.failed_requests,
    a.total_tokens,
    a.total_cost,
    a.unique_users,
    m.top_models,
    e.top_endpoints,
    d.daily_usage
  FROM aggregated a
  CROSS JOIN model_stats m
  CROSS JOIN endpoint_stats e
  CROSS JOIN daily_stats d;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT ON public.api_usage_logs TO authenticated;
GRANT INSERT ON public.api_usage_logs TO service_role;
GRANT EXECUTE ON FUNCTION public.calculate_api_cost TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_api_usage_stats TO authenticated;
