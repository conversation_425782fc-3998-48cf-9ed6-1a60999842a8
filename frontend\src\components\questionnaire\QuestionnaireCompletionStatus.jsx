import React from 'react';

/**
 * QuestionnaireCompletionStatus Component
 * 
 * A reusable component that displays the completion status of questionnaires
 * with consistent styling and behavior across different questionnaire pages.
 * 
 * @param {Object} props - Component props
 * @param {Array} props.questionnaires - Array of questionnaire objects
 * @param {string} props.questionnaires[].key - Unique identifier for the questionnaire
 * @param {string} props.questionnaires[].displayName - Display name for the questionnaire
 * @param {Object} props.completionStatus - Object mapping questionnaire keys to completion status
 * @param {boolean} props.loading - Whether the completion status is still loading
 * @param {string} props.title - Title for the completion status section
 * @param {number} props.columns - Number of columns in the grid (default: 2)
 */
function QuestionnaireCompletionStatus({
  questionnaires = [],
  completionStatus = {},
  loading = false,
  title = "Questionnaire Completion Status",
  columns = 2
}) {
  // Generate grid column classes
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  const gridClass = gridCols[columns] || gridCols[2];

  // Show loading only if we're actually loading AND don't have questionnaire data
  if (loading && questionnaires.length === 0) {
    return (
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="raleway-title-h3 mb-4 text-gray-800">{title}</h3>
        <div className="animate-pulse">
          <div className={`grid ${gridClass} gap-4`}>
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex items-center justify-between p-3 bg-white rounded border">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-6 bg-gray-200 rounded w-16"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 p-4 bg-gray-50 rounded-lg">
      <h3 className="raleway-title-h3 mb-4 text-gray-800">{title}</h3>
      <div className={`grid ${gridClass} gap-4`}>
        {questionnaires.map((questionnaire) => {
          const isCompleted = completionStatus[questionnaire.key] || false;
          
          return (
            <div 
              key={questionnaire.key}
              className="flex items-center justify-between p-3 bg-white rounded border"
            >
              <span className="body-text">
                {questionnaire.displayName}
              </span>
              <span 
                className={`px-2 py-1 rounded text-xs font-medium ${
                  isCompleted
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-600'
                }`}
              >
                {isCompleted ? 'Completed' : 'Not Started'}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default QuestionnaireCompletionStatus;
