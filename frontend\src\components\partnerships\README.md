# Partnership Components Documentation

This directory contains reusable components for partnership-related functionality across the market research tool. These components provide consistent styling, behavior, and user experience for partnership pages.

## Components Overview

### 1. WhyPartnerSection
A reusable section component that displays compelling value propositions for potential partners.

### 2. PartnershipJourneySection
A reusable section component that outlines the step-by-step partnership process.

---

## Component Details

### WhyPartnerSection

**Purpose**: Displays the "Why Partner With Us?" section with compelling value propositions and benefits for potential partners.

**Props**:
- `title` (string): Section title (default: "Why Partner With Us?")
- `description` (string): Section description text
- `benefits` (Array): Array of benefit objects
  - `title` (string): Benefit title
  - `description` (string): Benefit description
- `themeColor` (string): Theme color ('red', 'amber', 'blue', 'green', 'purple')
- `className` (string): Additional CSS classes

**Example Usage**:
```jsx
import WhyPartnerSection from './partnerships/WhyPartnerSection';

const benefits = [
  {
    title: "Market Expansion",
    description: "Access new customer segments and geographic markets"
  },
  {
    title: "Revenue Growth", 
    description: "Create new revenue streams through partnership models"
  }
];

<WhyPartnerSection
  title="Why Partner With Us?"
  description="Partnering with our platform opens doors to growth opportunities..."
  benefits={benefits}
  themeColor="purple"
/>
```

### PartnershipJourneySection

**Purpose**: Displays "The Partnership Journey" section with a structured approach to partnership development.

**Props**:
- `title` (string): Section title (default: "The Partnership Journey")
- `description` (string): Section description text
- `steps` (Array): Array of journey step objects
  - `number` (string): Step number
  - `title` (string): Step title
  - `description` (string): Step description
- `themeColor` (string): Theme color ('red', 'amber', 'blue', 'green', 'purple')
- `className` (string): Additional CSS classes
- `columns` (number): Grid columns (1-6, default: 4)

**Example Usage**:
```jsx
import PartnershipJourneySection from './partnerships/PartnershipJourneySection';

const journeySteps = [
  {
    number: "1",
    title: "Discovery",
    description: "Initial consultation and needs assessment"
  },
  {
    number: "2", 
    title: "Evaluation",
    description: "Mutual fit assessment and opportunity analysis"
  }
];

<PartnershipJourneySection
  title="The Partnership Journey"
  description="Our structured approach ensures successful partnership development..."
  steps={journeySteps}
  themeColor="purple"
  columns={4}
/>
```

---

## Design Features

### Consistent Theming
Both components support the same theme color system:
- **Red**: `bg-red-50`, `border-red-200`, `text-red-800`
- **Amber**: `bg-amber-50`, `border-amber-200`, `text-amber-800`
- **Blue**: `bg-blue-50`, `border-blue-200`, `text-blue-800`
- **Green**: `bg-green-50`, `border-green-200`, `text-green-800`
- **Purple**: `bg-purple-50`, `border-purple-200`, `text-purple-800`

**Note**: PartnershipJourneySection uses light blue (`bg-blue-400`) for step number circles to maintain visual consistency across all theme colors.

### Responsive Design
- **WhyPartnerSection**: Responsive list layout with proper spacing
- **PartnershipJourneySection**: Responsive grid that adapts from 1 column on mobile to specified columns on larger screens

### Typography
- Uses consistent typography classes: `raleway-title-h2`, `raleway-title-h4`, `body-text`
- Maintains visual hierarchy and readability

---

## Usage Guidelines

### When to Use WhyPartnerSection
- Partnership landing pages
- Value proposition sections
- Benefits overview sections
- Any page highlighting partnership advantages

### When to Use PartnershipJourneySection
- Process explanation sections
- Step-by-step guides
- Partnership onboarding flows
- Journey mapping displays

### Customization
Both components are highly customizable:
- **Content**: All text content can be customized via props
- **Styling**: Theme colors and additional CSS classes
- **Layout**: Grid columns and spacing can be adjusted
- **Benefits/Steps**: Completely customizable arrays of content

---

## Integration with Existing Pages

These components are designed to integrate seamlessly with:
- Existing questionnaire components (QuestionnaireButtonGrid, etc.)
- Current page layouts and styling
- Theme color systems used throughout the application
- Responsive design patterns

---

## AI Assistant Sections

### WhyPartnerSection AI Assistance
The WhyPartnerSection component helps users:
- Define compelling value propositions for partnerships
- Structure benefits in a clear, scannable format
- Maintain consistent messaging across partnership materials
- Adapt content for different partnership types or audiences

### PartnershipJourneySection AI Assistance  
The PartnershipJourneySection component helps users:
- Map out clear partnership processes
- Set proper expectations for potential partners
- Visualize the partnership lifecycle
- Communicate structured approaches to collaboration

Both components support the overall goal of creating comprehensive, professional partnership pages that effectively communicate value and process to potential partners.
