{"_instructions": {"description": "This file controls which AI models are available to different clients", "usage": "Add client email addresses as keys and set model availability as boolean values", "default_behavior": "If a client is not listed here, they get access to all models by default", "model_list": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo", "gemini-1.5-pro", "gemini-1.5-flash", "deepseek-chat"]}, "client_configurations": {"<EMAIL>": {"description": "Demo client - limited selection for demonstrations", "models": {"o1-preview": false, "o1-mini": false, "gpt-4o": false, "gpt-4-turbo": false, "claude-3-opus-20240229": false, "deepseek-chat": true, "gemini-1.5-flash": true, "claude-3-haiku-20240307": true}}}, "tier_configurations": {"free": {"description": "Free tier - basic models only", "models": {"o1-preview": false, "o1-mini": false, "gpt-4o": false, "gpt-4-turbo": false, "claude-3-opus-20240229": false, "claude-3-5-sonnet-20241022": false, "gemini-1.5-pro": false, "deepseek-chat": true, "gemini-1.5-flash": true, "claude-3-haiku-20240307": true, "gpt-3.5-turbo": true, "gemini-pro": true}}, "basic": {"description": "Basic tier - some premium models", "models": {"o1-preview": false, "o1-mini": true, "gpt-4o": false, "gpt-4-turbo": true, "claude-3-opus-20240229": false, "claude-3-5-sonnet-20241022": true, "gemini-1.5-pro": true}}, "premium": {"description": "Premium tier - all models available", "models": {}}}}