# Questionnaire Components Documentation

This directory contains reusable components for questionnaire functionality across the market research tool. These components provide consistent styling, behavior, and user experience across different questionnaire pages.

## Components Overview

### 1. QuestionnaireButtonGrid
A reusable grid component that displays questionnaire options as clickable cards.

### 2. QuestionnaireCompletionStatus  
A status display component showing completion progress for multiple questionnaires.

### 3. StrategyGenerationSection
A section component for generating strategies based on completed questionnaires.

### 4. AIAgent (Existing)
An AI assistant component that provides contextual help and answers questions.

---

## Component Details

### QuestionnaireButtonGrid

**Purpose**: Renders a responsive grid of questionnaire cards with consistent styling and interaction patterns.

**Props**:
- `questionnaires` (Array): Array of questionnaire objects
  - `key` (string): Unique identifier
  - `title` (string): Display title
  - `description` (string): Description text
- `activeQuestionnaire` (string): Currently active questionnaire key
- `onQuestionnaireToggle` (Function): Callback when questionnaire is clicked
- `themeColor` (string): Theme color ('red', 'amber', 'blue', 'green', 'purple')
- `columns` (number): Grid columns (1-4, default: 2)

**Example Usage**:
```jsx
import QuestionnaireButtonGrid from './questionnaire/QuestionnaireButtonGrid';

const questionnaires = [
  {
    key: 'competitorProfiling',
    title: 'Competitor Profiling',
    description: 'Create detailed profiles of your main competitors.'
  },
  // ... more questionnaires
];

<QuestionnaireButtonGrid
  questionnaires={questionnaires}
  activeQuestionnaire={activeQuestionnaire}
  onQuestionnaireToggle={toggleQuestionnaire}
  themeColor="red"
  columns={2}
/>
```

### QuestionnaireCompletionStatus

**Purpose**: Displays completion status for multiple questionnaires with loading states.

**Props**:
- `questionnaires` (Array): Array of questionnaire objects
  - `key` (string): Unique identifier
  - `displayName` (string): Display name for status
- `completionStatus` (Object): Mapping of questionnaire keys to boolean completion status
- `loading` (boolean): Whether status is loading
- `title` (string): Section title
- `columns` (number): Grid columns (1-4, default: 2)

**Example Usage**:
```jsx
import QuestionnaireCompletionStatus from './questionnaire/QuestionnaireCompletionStatus';

const questionnaires = [
  { key: 'competitorProfiling', displayName: 'Competitor Profiling' },
  // ... more questionnaires
];

const completionStatus = {
  competitorProfiling: true,
  swotAnalysis: false
};

<QuestionnaireCompletionStatus
  questionnaires={questionnaires}
  completionStatus={completionStatus}
  loading={loading}
  title="Questionnaire Completion Status"
  columns={2}
/>
```

### StrategyGenerationSection

**Purpose**: Provides strategy generation functionality with navigation or custom callbacks.

**Props**:
- `title` (string): Section title
- `description` (string): Description text
- `buttonText` (string): Button text
- `strategyType` (string): Strategy type for sessionStorage
- `completionStatus` (Object): Questionnaire completion status
- `themeColor` (string): Theme color
- `onGenerateStrategy` (Function): Custom callback (optional)
- `disabled` (boolean): Whether button is disabled
- `navigateTo` (string): Navigation route (default: '/strategy')

**Example Usage**:
```jsx
import StrategyGenerationSection from './questionnaire/StrategyGenerationSection';

<StrategyGenerationSection
  title="GENERATE COMPETITIVE ANALYSIS STRATEGY"
  description="Ready to turn your responses into actionable strategy?"
  buttonText="Generate Strategy"
  strategyType="Competition Analysis Questionnaires"
  completionStatus={completedQuestionnaires}
  themeColor="red"
  navigateTo="/strategy"
/>
```

### AIAgent (Existing Component)

**Purpose**: Provides AI-powered assistance and answers questions related to the current page context.

**Props**:
- `title` (string): Assistant title
- `description` (string): Assistant description
- `contextPrompt` (string): Context prompt for AI responses

**Example Usage**:
```jsx
import AIAgent from './AIAgent';

const contextPrompt = `You are a competition analysis expert...`;

<AIAgent
  title="Competition Analysis Assistant"
  description="Ask questions about competitive analysis frameworks and strategies."
  contextPrompt={contextPrompt}
/>
```

---

## Theme Colors

All components support consistent theme colors:
- `red`: Red color scheme (default)
- `amber`: Amber/yellow color scheme  
- `blue`: Blue color scheme
- `green`: Green color scheme
- `purple`: Purple color scheme

---

## Implementation Benefits

### Code Reusability
- Eliminates duplicate code across 5+ questionnaire pages
- Consistent behavior and styling
- Single source of truth for questionnaire UI patterns

### Maintainability  
- Changes only need to be made in one place
- Easier to add new features or fix bugs
- Consistent API across all questionnaire pages

### Consistency
- Uniform user experience across all pages
- Standardized interaction patterns
- Consistent visual design

### Flexibility
- Configurable themes and layouts
- Support for different numbers of questionnaires
- Customizable callbacks and navigation

---

## Migration Guide

To migrate existing questionnaire pages to use these components:

1. Import the required components
2. Extract questionnaire data into the expected format
3. Replace existing JSX with component usage
4. Pass appropriate props for styling and behavior
5. Test functionality and styling

Example migration for CompetitionAnalysis.jsx:
```jsx
// Before: Inline JSX grid
<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
  {/* Multiple div elements with questionnaire cards */}
</div>

// After: Component usage
<QuestionnaireButtonGrid
  questionnaires={questionnaireData}
  activeQuestionnaire={activeQuestionnaire}
  onQuestionnaireToggle={toggleQuestionnaire}
  themeColor="red"
/>
```

---

## Complete Implementation Example

Here's a complete example showing how to implement all components in a questionnaire page:

```jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AIAgent from './AIAgent';
import QuestionnaireLoader from './QuestionnaireLoader';
import QuestionnaireButtonGrid from './questionnaire/QuestionnaireButtonGrid';
import QuestionnaireCompletionStatus from './questionnaire/QuestionnaireCompletionStatus';
import StrategyGenerationSection from './questionnaire/StrategyGenerationSection';
import { useAuth } from '../context/AuthContext';

function ExampleQuestionnairePage() {
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null);
  const [loading, setLoading] = useState(true);
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    questionnaire1: false,
    questionnaire2: false,
    questionnaire3: false,
    questionnaire4: false
  });

  // Questionnaire data for the button grid
  const questionnaireData = [
    {
      key: 'questionnaire1',
      title: 'First Questionnaire',
      description: 'Description for the first questionnaire.'
    },
    {
      key: 'questionnaire2',
      title: 'Second Questionnaire',
      description: 'Description for the second questionnaire.'
    },
    // ... more questionnaires
  ];

  // Questionnaire data for completion status
  const statusQuestionnaires = [
    { key: 'questionnaire1', displayName: 'First Questionnaire' },
    { key: 'questionnaire2', displayName: 'Second Questionnaire' },
    // ... more questionnaires
  ];

  const toggleQuestionnaire = (questionnaireType) => {
    setActiveQuestionnaire(activeQuestionnaire === questionnaireType ? null : questionnaireType);
  };

  const contextPrompt = `Your AI assistant context prompt here...`;

  return (
    <div className="space-y-8">
      <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
        <h2 className="raleway-title-h2 mb-4">Page Title</h2>
        <p className="body-text mb-4">Page description</p>

        <QuestionnaireButtonGrid
          questionnaires={questionnaireData}
          activeQuestionnaire={activeQuestionnaire}
          onQuestionnaireToggle={toggleQuestionnaire}
          themeColor="amber"
          columns={2}
        />

        <QuestionnaireCompletionStatus
          questionnaires={statusQuestionnaires}
          completionStatus={completedQuestionnaires}
          loading={loading}
          title="Questionnaire Completion Status"
          columns={2}
        />

        <StrategyGenerationSection
          title="GENERATE YOUR STRATEGY"
          description="Ready to turn your responses into actionable strategy?"
          buttonText="Generate Strategy"
          strategyType="Your Strategy Type"
          completionStatus={completedQuestionnaires}
          themeColor="amber"
          navigateTo="/strategy"
        />
      </div>

      {/* Questionnaire Section */}
      {activeQuestionnaire && (
        <div className="mb-8 p-6 bg-white rounded-lg border border-gray-200 shadow-md">
          <QuestionnaireLoader
            // ... questionnaire loader props
          />
        </div>
      )}

      {/* AI Agent */}
      <AIAgent
        title="Your Assistant"
        description="Ask questions about your topic."
        contextPrompt={contextPrompt}
      />
    </div>
  );
}

export default ExampleQuestionnairePage;
```
