-- Migration script to simplify company_profiles table back to single company per user
-- Run this in your Supabase SQL editor to restore the one-company-per-account model

-- First, handle any duplicate records by keeping only the primary one (or first one if no primary)
DO $$ 
DECLARE
    user_record RECORD;
    keep_record_id UUID;
BEGIN
    -- For each user that has multiple company profiles
    FOR user_record IN 
        SELECT user_id, COUNT(*) as profile_count
        FROM public.company_profiles 
        GROUP BY user_id 
        HAVING COUNT(*) > 1
    LOOP
        -- Find the record to keep (primary if exists, otherwise first one)
        SELECT id INTO keep_record_id
        FROM public.company_profiles 
        WHERE user_id = user_record.user_id
        ORDER BY 
            CASE WHEN is_primary = true THEN 0 ELSE 1 END,
            created_at ASC
        LIMIT 1;
        
        -- Delete all other records for this user
        DELETE FROM public.company_profiles 
        WHERE user_id = user_record.user_id 
        AND id != keep_record_id;
        
        RAISE NOTICE 'Cleaned up % duplicate records for user %', (user_record.profile_count - 1), user_record.user_id;
    END LOOP;
END $$;

-- Add back the unique constraint on user_id to enforce one company per user
DO $$
BEGIN
    -- Check if the unique constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'company_profiles' 
        AND constraint_type = 'UNIQUE' 
        AND constraint_name = 'company_profiles_user_id_key'
    ) THEN
        ALTER TABLE public.company_profiles 
        ADD CONSTRAINT company_profiles_user_id_key UNIQUE (user_id);
        RAISE NOTICE 'Added unique constraint on user_id';
    ELSE
        RAISE NOTICE 'Unique constraint on user_id already exists';
    END IF;
END $$;

-- Optional: Remove the is_primary column since it's no longer needed
-- Uncomment the following lines if you want to remove this column
/*
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'company_profiles' 
        AND column_name = 'is_primary'
    ) THEN
        ALTER TABLE public.company_profiles DROP COLUMN is_primary;
        RAISE NOTICE 'Removed is_primary column';
    END IF;
END $$;
*/

-- Update table comment
COMMENT ON TABLE public.company_profiles IS 'Stores company profile information for users - one company per user account';

-- Update RLS policies to be simpler (remove multi-company logic)
DROP POLICY IF EXISTS select_own_company_profiles ON public.company_profiles;
DROP POLICY IF EXISTS insert_own_company_profiles ON public.company_profiles;
DROP POLICY IF EXISTS update_own_company_profiles ON public.company_profiles;
DROP POLICY IF EXISTS delete_own_company_profiles ON public.company_profiles;

-- Create simplified policies for single company per user
DO $$
BEGIN
    -- Create select policy
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'company_profiles'
        AND policyname = 'select_own_company_profile'
    ) THEN
        CREATE POLICY select_own_company_profile ON public.company_profiles
          FOR SELECT USING (auth.uid() = user_id);
        RAISE NOTICE 'Created select policy';
    END IF;

    -- Create insert policy
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'company_profiles'
        AND policyname = 'insert_own_company_profile'
    ) THEN
        CREATE POLICY insert_own_company_profile ON public.company_profiles
          FOR INSERT WITH CHECK (auth.uid() = user_id);
        RAISE NOTICE 'Created insert policy';
    END IF;

    -- Create update policy
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'company_profiles'
        AND policyname = 'update_own_company_profile'
    ) THEN
        CREATE POLICY update_own_company_profile ON public.company_profiles
          FOR UPDATE USING (auth.uid() = user_id);
        RAISE NOTICE 'Created update policy';
    END IF;

    -- Create delete policy
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'company_profiles'
        AND policyname = 'delete_own_company_profile'
    ) THEN
        CREATE POLICY delete_own_company_profile ON public.company_profiles
          FOR DELETE USING (auth.uid() = user_id);
        RAISE NOTICE 'Created delete policy';
    END IF;
END $$;

-- Show final status
DO $$
DECLARE
    total_users INTEGER;
    total_profiles INTEGER;
    users_with_profiles INTEGER;
BEGIN
    SELECT COUNT(DISTINCT user_id) INTO total_users FROM public.company_profiles;
    SELECT COUNT(*) INTO total_profiles FROM public.company_profiles;
    SELECT COUNT(DISTINCT user_id) INTO users_with_profiles FROM public.company_profiles;
    
    RAISE NOTICE 'Migration completed successfully!';
    RAISE NOTICE 'Total users with profiles: %', users_with_profiles;
    RAISE NOTICE 'Total company profiles: %', total_profiles;
    
    IF total_users = total_profiles THEN
        RAISE NOTICE 'SUCCESS: One company profile per user confirmed';
    ELSE
        RAISE WARNING 'WARNING: Still have % users with % profiles', total_users, total_profiles;
    END IF;
END $$;
